import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/widgets/form_builder_fields/form_builder_color_picker.dart';
import 'package:paperless_mobile/features/edit_label/view/add_label_page.dart';
import 'package:paperless_mobile/features/labels/cubit/label_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class AddTagPage extends StatelessWidget {
  final String? initialName;
  const AddTagPage({super.key, this.initialName});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LabelCubit(
        context.read(),
      ),
      child: AddLabelPage<Tag>(
        hinText: S.of(context)!.enterName,
        title: S.of(context)!.name,
        subTitle: 'Matching Algorithm',
        pageTitle: Text(
          S.of(context)!.addTag,
          style: AppTextStyles.textStyleAppBar,
        ),
        fromJsonT: Tag.fromJson,
        initialName: initialName,
        onSubmit: (context, label) => context.read<LabelCubit>().addTag(label),
        additionalFields: [
          Text(S.of(context)!.color,
              style: const TextStyle(fontWeight: FontWeight.w600)),
          FormBuilderColorPickerField(
            name: Tag.colorKey,
            valueTransformer: (color) => "#${color?.value.toRadixString(16)}",
            decoration: InputDecoration(
              fillColor: AppColor.white,
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              // label: Text(S.of(context)!.color),
            ),
            colorPickerType: ColorPickerType.materialPicker,
            initialValue: Color((Random().nextDouble() * 0xFFFFFF).toInt())
                .withOpacity(1.0),
            readOnly: true,
          ),
          FormBuilderField<bool>(
            name: Tag.isInboxTagKey,
            initialValue: false,
            builder: (field) {
              return Row(
                children: [
                  Checkbox(
                    activeColor: AppColor.primary,
                    value: field.value,
                    onChanged: (value) {
                      field.didChange(value);
                    },
                  ),
                  Text(S.of(context)!.inboxTag)
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
