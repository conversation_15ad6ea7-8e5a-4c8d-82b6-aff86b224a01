import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.white,
      body: Center(
          child: SizedBox(
              height: 120,
              width: 120,
              child: Image.asset('assets/logos/logo_and_name.png'))),
    );
  }
}
