import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class SelectProjectPage extends StatefulWidget {
  SelectProjectPage(
      {super.key,
      required this.documentUploadCubit,
      this.updateProject,
      this.isShowBottom = false});
  final DocumentUploadCubit documentUploadCubit;
  final bool isShowBottom;
  VoidCallback? updateProject;

  @override
  State<SelectProjectPage> createState() => _SelectProjectPageState();
}

class _SelectProjectPageState extends State<SelectProjectPage> {
  bool isSelected = false;
  void checkSelected() {
    final selectedProjects =
        widget.documentUploadCubit.state.projects.where((e) => e.isSelected);
    if (selectedProjects.isNotEmpty) {
      setState(() {
        isSelected = true;
      });
    } else {
      setState(() {
        isSelected = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title:  Row(children: [
          Text(
            S.of(context)!.project,
            style: AppTextStyles.textStyleAppBar,
          )
        ]),
      ),
      bottomNavigationBar: widget.isShowBottom
          ? Padding(
              padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
              child: GestureDetector(
                onTap: () {
                  if (isSelected) {
                    Navigator.pop(context);
                    widget.updateProject?.call();
                  }
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 48,
                  decoration: BoxDecoration(
                      color:
                          isSelected ? AppColor.primary : AppColor.grey_DADADA,
                      borderRadius: BorderRadius.circular(10)),
                  child: Text(
                    'Save',
                    style: AppTextStyles.textStyle14.copyWith(
                        color: AppColor.white, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            )
          : null,
      body: Padding(
          padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
          child: BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
            builder: (context, state) {
              return ListView.separated(
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        widget.documentUploadCubit.selectedProject(index);
                        checkSelected();
                      },
                      child: Container(
                        height: 46,
                        width: size.width,
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: state.projects[index].isSelected == true
                                ? AppColor.blue_024CAA.withOpacity(0.1)
                                : AppColor.white),
                        child: Row(
                          children: [
                            Text(
                              state.projects[index].name,
                              style: AppTextStyles.textStyle14
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                            const Spacer(),
                            if (state.projects[index].isSelected == true)
                              SvgPicture.asset('assets/svgs/tick.svg')
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => const Gap(16),
                  itemCount: state.projects.length);
            },
          )),
    );
  }
}
