import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/features/document_upload/view/users_page.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class GroupPage extends StatelessWidget {
  const GroupPage(
      {super.key, required this.documentUploadCubit, required this.type});
  final DocumentUploadCubit documentUploadCubit;
  final UserType type;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return BlocProvider.value(
      value: documentUploadCubit,
      child: Scaffold(
        appBar: AppBar(
          iconTheme: const IconThemeData(color: AppColor.primary),
          title: Row(
            children: [
              Text(
                S.of(context)!.groups,
                style: AppTextStyles.textStyleAppBar,
              )
            ],
          ),
        ),
        body: Padding(
            padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
            child: BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
              builder: (context, state) {
                final departments = type == UserType.view
                    ? state.groupViews
                    : state.groupEditor;

                return ListView.separated(
                  itemBuilder: (context, index) {
                    final department = departments[index];
                    return GestureDetector(
                      onTap: () {
                        if (type == UserType.view) {
                          documentUploadCubit.selectGroupViews(index);
                        } else {
                          documentUploadCubit.selectGroupEditor(index);
                        }
                        documentUploadCubit.checkSelectedPermission();
                      },
                      child: Container(
                        height: 46,
                        width: size.width,
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: department.isSelected
                              ? AppColor.blue_024CAA.withOpacity(0.1)
                              : AppColor.white,
                        ),
                        child: Row(
                          children: [
                            Text(
                              department.name,
                              style: AppTextStyles.textStyle14
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                            const Spacer(),
                            if (department.isSelected)
                              SvgPicture.asset('assets/svgs/tick.svg'),
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => const Gap(16),
                  itemCount: departments.length,
                );
              },
            )),
      ),
    );
  }
}
