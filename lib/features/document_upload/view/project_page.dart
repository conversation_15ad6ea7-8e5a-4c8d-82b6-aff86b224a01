import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/select_project_page.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class ProjectPage extends StatelessWidget {
  ProjectPage({super.key, required this.documentUploadCubit});
  DocumentUploadCubit documentUploadCubit;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: Row(children: [
          Text(
            S.of(context)!.projects,
            style: AppTextStyles.textStyleAppBar,
          )
        ]),
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context)!.project.toUpperCase(),
              style: AppTextStyles.textStyleBold14
                  .copyWith(color: AppColor.grey_909090),
            ),
            const Gap(6),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: SelectProjectPage(
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ));
              },
              child: Container(
                height: 46,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.circular(10)),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        S.of(context)!.selectProject,
                        style: AppTextStyles.textStyle14.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColor.black_3C3C43.withOpacity(0.6)),
                      ),
                      const Icon(Icons.chevron_right)
                    ]),
              ),
            ),
            const Gap(10),
            Text(
              S.of(context)!.selectTheProjectToAttachFilesTo,
              style: AppTextStyles.textStyle14
                  .copyWith(color: AppColor.grey_909090),
            )
          ],
        ),
      ),
    );
  }
}
