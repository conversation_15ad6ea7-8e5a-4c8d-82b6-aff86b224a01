import 'dart:async';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/widgets/dialog_utils/pop_with_unsaved_changes.dart';
import 'package:paperless_mobile/core/widgets/form_builder_fields/form_builder_localized_date_picker.dart';
import 'package:paperless_mobile/core/widgets/material/colored_tab_bar.dart';
import 'package:paperless_mobile/core/workarounds/colored_chip.dart';
import 'package:paperless_mobile/features/document_edit/cubit/document_edit_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/select_department_page.dart';
import 'package:paperless_mobile/features/document_upload/view/permissions_page.dart';
import 'package:paperless_mobile/features/document_upload/view/project_page.dart';
import 'package:paperless_mobile/features/documents/view/pages/document_view.dart';
import 'package:paperless_mobile/features/labels/tags/view/widgets/tags_form_field.dart';
import 'package:paperless_mobile/features/labels/view/widgets/label_form_field.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/labels_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T itemData);

class DocumentEditPage extends StatefulWidget {
  const DocumentEditPage({super.key});

  @override
  State<DocumentEditPage> createState() => _DocumentEditPageState();
}

class _DocumentEditPageState extends State<DocumentEditPage>
    with SingleTickerProviderStateMixin {
  static const fkTitle = "title";
  static const fkCorrespondent = "correspondent";
  static const fkTags = "tags";
  static const fkDocumentType = "documentType";
  static const fkCreatedDate = "createdAtDate";
  static const fkStoragePath = 'storagePath';
  static const fkContent = 'content';

  final _formKey = GlobalKey<FormBuilderState>();
  late DocumentEditCubit documentEditCubit;
  late DocumentUploadCubit documentUploadCubit;

  bool _isShowingPdf = false;
  Future<Uint8List>? _documentBytes;
  bool _hasLoadingError = false;
  bool _isPdfLoaded = false;

  late final AnimationController _animationController;
  late final Animation<double> _animation;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    documentEditCubit = context.read();
    documentUploadCubit = context.read();
    documentUploadCubit
        .initDataProject(documentEditCubit.state.document.projects);

    documentUploadCubit.initData(
        userViews:
            documentEditCubit.state.document.permissions?.view.users ?? [],
        groupViews:
            documentEditCubit.state.document.permissions?.view.groups ?? [],
        userEditor:
            documentEditCubit.state.document.permissions?.change.users ?? [],
        groupEditor:
            documentEditCubit.state.document.permissions?.change.groups ?? [],
        departments: documentEditCubit.state.document.departments);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _animation =
        CurvedAnimation(parent: _animationController, curve: Curves.easeInCubic)
            .drive(Tween<double>(begin: 0, end: 1));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Safely load document bytes with error handling and memory optimization
  Future<Uint8List> _loadDocumentBytes(int documentId) async {
    if (_documentBytes != null) {
      return _documentBytes!;
    }

    try {
      _documentBytes =
          context.read<PaperlessDocumentsApi>().downloadDocument(documentId);

      final bytes = await _documentBytes!;
      if (mounted) {
        setState(() {
          _isPdfLoaded = true;
          _hasLoadingError = false;
        });
      }
      return bytes;
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasLoadingError = true;
          _isPdfLoaded = false;
        });
      }
      rethrow;
    }
  }

  /// Build optimized document view with error handling and lazy loading
  Widget _buildOptimizedDocumentView(DocumentEditState state) {
    return FutureBuilder<Uint8List>(
      future: _loadDocumentBytes(state.document.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            color: Colors.grey[100],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading document...'),
                ],
              ),
            ),
          );
        }

        if (snapshot.hasError || _hasLoadingError) {
          return Container(
            color: Colors.grey[100],
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Failed to load document',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _documentBytes = null;
                        _hasLoadingError = false;
                        _isPdfLoaded = false;
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        if (snapshot.hasData) {
          return DocumentView(
            showAppBar: false,
            showControls: false,
            title: state.document.title,
            bytes: Future.value(snapshot.data!),
          );
        }

        return Container(
          color: Colors.grey[100],
          child: const Center(
            child: Text('No document available'),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = context.watch<LocalUserAccount>().paperlessUser;
    return BlocBuilder<DocumentEditCubit, DocumentEditState>(
      builder: (context, state) {
        print(state.document.permissions?.view.users);
        print('-----------------------------------');
        final filteredSuggestions = state.suggestions;
        return PopWithUnsavedChanges(
          hasChangesPredicate: () {
            final fkState = _formKey.currentState;
            if (fkState == null) {
              return false;
            }
            final doc = state.document;
            final (
              title,
              correspondent,
              documentType,
              storagePath,
              tags,
              createdAt,
              content
            ) = _currentValues;
            final isContentTouched =
                _formKey.currentState?.fields[fkContent]?.isDirty ?? false;
            return doc.title != title ||
                doc.correspondent != correspondent ||
                doc.documentType != documentType ||
                doc.storagePath != storagePath ||
                !const UnorderedIterableEquality().equals(doc.tags, tags) ||
                doc.created != createdAt ||
                (doc.content != content && isContentTouched);
          },
          child: FormBuilder(
            key: _formKey,
            child: Scaffold(
              bottomNavigationBar: Padding(
                padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
                child: GestureDetector(
                  onTap: () {
                    final List<int> departmentSelected = documentUploadCubit
                        .state.departments
                        .where((e) => e.isSelected)
                        .map((e) => e.id)
                        .toList();
                    _onSubmit(state.document, departmentSelected);
                    documentUploadCubit.addProject(
                        documentEditCubit.state.document.id.toString());
                    documentUploadCubit.addPermission(
                        documentEditCubit.state.document.id.toString());
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 48,
                    decoration: BoxDecoration(
                        color: AppColor.primary,
                        borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      S.of(context)!.saveChanges,
                      style: AppTextStyles.textStyle14.copyWith(
                          color: AppColor.white, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ),
              appBar: AppBar(
                iconTheme: const IconThemeData(color: AppColor.primary),
                title: Row(
                  children: [
                    Text(S.of(context)!.editDocument,
                        style: AppTextStyles.textStyleAppBar),
                  ],
                ),
                actions: [
                  IconButton(
                    tooltip: _isShowingPdf
                        ? S.of(context)!.hidePdf
                        : S.of(context)!.showPdf,
                    padding: const EdgeInsets.all(12),
                    icon: AnimatedCrossFade(
                      duration: _animationController.duration!,
                      reverseDuration: _animationController.reverseDuration,
                      crossFadeState: _isShowingPdf
                          ? CrossFadeState.showFirst
                          : CrossFadeState.showSecond,
                      firstChild: const Icon(Icons.visibility_off_outlined),
                      secondChild: const Icon(Icons.visibility_outlined),
                    ),
                    onPressed: () {
                      if (_isShowingPdf) {
                        setState(() {
                          _isShowingPdf = false;
                        });
                        _animationController.reverse();
                      } else {
                        setState(() {
                          _isShowingPdf = true;
                        });
                        _animationController.forward();
                      }
                    },
                  )
                ],
              ),
              body: Stack(
                children: [
                  DefaultTabController(
                    length: 2,
                    child: Scaffold(
                      resizeToAvoidBottomInset: true,
                      // floatingActionButton: !_isShowingPdf
                      //     ? FloatingActionButton.extended(
                      //         heroTag: "fab_document_edit",
                      //         onPressed: () => _onSubmit(state.document),
                      //         icon: const Icon(Icons.save),
                      //         label: Text(S.of(context)!.saveChanges),
                      //       )
                      //     : null,
                      appBar: ColoredTabBar(
                        color: AppColor.white,
                        tabBar: TabBar(
                          labelColor: AppColor.primary,
                          unselectedLabelColor: AppColor.black_333333,
                          indicatorColor: AppColor.primary,
                          tabs: [
                            Tab(text: S.of(context)!.overview),
                            Tab(text: S.of(context)!.content),
                          ],
                        ),
                      ),
                      extendBody: true,
                      body: _buildEditForm(
                        context,
                        state,
                        filteredSuggestions,
                        currentUser,
                      ),
                    ),
                  ),
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Transform.scale(
                        alignment: Alignment.bottomLeft,
                        scale: _animation.value,
                        child: _buildOptimizedDocumentView(state),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Padding _buildEditForm(
    BuildContext context,
    DocumentEditState state,
    FieldSuggestions? filteredSuggestions,
    UserModel currentUser,
  ) {
    final labelRepository = context.watch<LabelRepository>();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: TabBarView(
        physics: const NeverScrollableScrollPhysics(),
        children: [
          ListView(
            children: [
              const SizedBox(height: 16),
              _buildTitleFormField(state.document.title).padded(),
              _buildCreatedAtFormField(
                state.document.created,
                filteredSuggestions,
              ).padded(),
              // Correspondent form field
              if (currentUser.canViewCorrespondents)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context)!.correspondent,
                      style: AppTextStyles.textStyle14
                          .copyWith(fontWeight: FontWeight.w600),
                    ),
                    const Gap(8),
                    Container(
                      decoration: BoxDecoration(
                          color: AppColor.white,
                          borderRadius: BorderRadius.circular(10)),
                      child: LabelFormField<Correspondent>(
                        titleAppBar: S.of(context)!.correspondent,
                        showAnyAssignedOption: false,
                        showNotAssignedOption: false,
                        onAddLabel: (currentInput) => CreateLabelRoute(
                          LabelType.correspondent,
                          name: currentInput,
                        ).push<Correspondent>(context),
                        addLabelText: S.of(context)!.addCorrespondent,
                        labelText: S.of(context)!.correspondent,
                        options: labelRepository.correspondents,
                        initialValue: state.document.correspondent != null
                            ? SetIdQueryParameter(
                                id: state.document.correspondent!)
                            : const UnsetIdQueryParameter(),
                        name: fkCorrespondent,
                        prefixIcon: const Icon(Icons.person_outlined),
                        allowSelectUnassigned: true,
                        canCreateNewLabel: currentUser.canCreateCorrespondents,
                        suggestions: filteredSuggestions?.correspondents ?? [],
                      ),
                    ),
                  ],
                ).padded(),
              // DocumentType form field
              if (currentUser.canViewDocumentTypes)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context)!.documentType,
                      style: AppTextStyles.textStyle14
                          .copyWith(fontWeight: FontWeight.w600),
                    ),
                    const Gap(8),
                    Container(
                      decoration: BoxDecoration(
                          color: AppColor.white,
                          borderRadius: BorderRadius.circular(10)),
                      child: LabelFormField<DocumentType>(
                        titleAppBar: S.of(context)!.documentType,
                        showAnyAssignedOption: false,
                        showNotAssignedOption: false,
                        onAddLabel: (currentInput) => CreateLabelRoute(
                          LabelType.documentType,
                          name: currentInput,
                        ).push<DocumentType>(context),
                        canCreateNewLabel: currentUser.canCreateDocumentTypes,
                        addLabelText: S.of(context)!.addDocumentType,
                        labelText: S.of(context)!.documentType,
                        initialValue: state.document.documentType != null
                            ? SetIdQueryParameter(
                                id: state.document.documentType!)
                            : const UnsetIdQueryParameter(),
                        options: labelRepository.documentTypes,
                        name: _DocumentEditPageState.fkDocumentType,
                        prefixIcon: const Icon(Icons.description_outlined),
                        allowSelectUnassigned: true,
                        suggestions: filteredSuggestions?.documentTypes ?? [],
                      ),
                    ),
                  ],
                ).padded(),
              // StoragePath form field
              if (currentUser.canViewStoragePaths)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context)!.storagePath,
                      style: AppTextStyles.textStyle14
                          .copyWith(fontWeight: FontWeight.w600),
                    ),
                    const Gap(8),
                    Container(
                      decoration: BoxDecoration(
                          color: AppColor.white,
                          borderRadius: BorderRadius.circular(10)),
                      child: LabelFormField<StoragePath>(
                        titleAppBar: S.of(context)!.storagePath,
                        showAnyAssignedOption: false,
                        showNotAssignedOption: false,
                        onAddLabel: (currentInput) => CreateLabelRoute(
                          LabelType.storagePath,
                          name: currentInput,
                        ).push<StoragePath>(context),
                        canCreateNewLabel: currentUser.canCreateStoragePaths,
                        addLabelText: S.of(context)!.addStoragePath,
                        labelText: 'Enter here',
                        options: labelRepository.storagePaths,
                        initialValue: state.document.storagePath != null
                            ? SetIdQueryParameter(
                                id: state.document.storagePath!)
                            : const UnsetIdQueryParameter(),
                        name: fkStoragePath,
                        prefixIcon: const Icon(Icons.folder_outlined),
                        allowSelectUnassigned: true,
                      ),
                    ),
                  ],
                ).padded(),
              // Tag form field
              if (currentUser.canViewTags)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context)!.tags,
                      style: AppTextStyles.textStyle14
                          .copyWith(fontWeight: FontWeight.w600),
                    ).paddedOnly(left: 10, top: 10),
                    TagsFormField(
                      options: labelRepository.tags,
                      name: fkTags,
                      allowOnlySelection: true,
                      allowCreation: true,
                      allowExclude: false,
                      suggestions: filteredSuggestions?.tags ?? [],
                      initialValue: IdsTagsQuery(
                        include: state.document.tags.toList(),
                      ),
                    ).padded(),
                  ],
                ),

              const Gap(20),
              const Text(
                'Permissions',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ).paddedOnly(left: 8),
              const Gap(6),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => BlocProvider.value(
                          value: documentUploadCubit,
                          child: PermissionsPage(
                              documentUploadCubit: documentUploadCubit),
                        ),
                      ));
                },
                child: Container(
                  height: 46,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      color: AppColor.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Edit permission',
                          style: AppTextStyles.textStyle14.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColor.black_3C3C43.withOpacity(0.6)),
                        ),
                        const Icon(Icons.chevron_right)
                      ]),
                ),
              ).paddedOnly(left: 8),
              const Gap(20),
              const Text(
                'Projects',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ).paddedOnly(left: 8),
              const Gap(6),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ProjectPage(
                            documentUploadCubit: documentUploadCubit),
                      ));
                },
                child: Container(
                  height: 46,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      color: AppColor.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Edit project assignment',
                          style: AppTextStyles.textStyle14.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColor.black_3C3C43.withOpacity(0.6)),
                        ),
                        const Icon(Icons.chevron_right)
                      ]),
                ),
              ).paddedOnly(left: 8),

              const Gap(20),
              const Text(
                'Departments',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ).paddedOnly(left: 8),
              const Gap(6),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SelectDepartmentPage(
                            documentUploadCubit: documentUploadCubit),
                      ));
                },
                child: Container(
                  height: 46,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      color: AppColor.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Edit department assignment',
                          style: AppTextStyles.textStyle14.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColor.black_3C3C43.withOpacity(0.6)),
                        ),
                        const Icon(Icons.chevron_right)
                      ]),
                ),
              ).paddedOnly(left: 8),

              const SizedBox(height: 140),
            ],
          ),
          SingleChildScrollView(
            child: Column(
              children: [
                FormBuilderTextField(
                  name: fkContent,
                  maxLines: null,
                  keyboardType: TextInputType.multiline,
                  initialValue: state.document.content,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                  ),
                ),
                const SizedBox(height: 84),
              ],
            ),
          ),
        ],
      ),
    );
  }

  (
    String? title,
    int? correspondent,
    int? documentType,
    int? storagePath,
    List<int>? tags,
    DateTime? createdAt,
    String? content,
  ) get _currentValues {
    final fkState = _formKey.currentState!;

    final correspondentParam =
        fkState.getRawValue<IdQueryParameter?>(fkCorrespondent);
    final documentTypeParam =
        fkState.getRawValue<IdQueryParameter?>(fkDocumentType);
    final storagePathParam =
        fkState.getRawValue<IdQueryParameter?>(fkStoragePath);
    final tagsParam = fkState.getRawValue<TagsQuery?>(fkTags);
    final title = fkState.getRawValue<String?>(fkTitle);
    final created = fkState.getRawValue<FormDateTime?>(fkCreatedDate);
    final correspondent = switch (correspondentParam) {
      SetIdQueryParameter(id: var id) => id,
      _ => null,
    };
    final documentType = switch (documentTypeParam) {
      SetIdQueryParameter(id: var id) => id,
      _ => null,
    };
    final storagePath = switch (storagePathParam) {
      SetIdQueryParameter(id: var id) => id,
      _ => null,
    };
    final tags = switch (tagsParam) {
      IdsTagsQuery(include: var i) => i,
      _ => null,
    };
    final content = fkState.getRawValue<String?>(fkContent);

    return (
      title,
      correspondent,
      documentType,
      storagePath,
      tags,
      created?.toDateTime(),
      content
    );
  }

  Future<void> _onSubmit(
      DocumentModel document, List<int> departmentSelected) async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      context.pop();
      final (
        title,
        correspondent,
        documentType,
        storagePath,
        tags,
        createdAt,
        content
      ) = _currentValues;
      var mergedDocument = document.copyWith(
          title: title,
          created: createdAt,
          correspondent: () => correspondent,
          documentType: () => documentType,
          storagePath: () => storagePath,
          tags: tags,
          content: content,
          departments: departmentSelected);

      try {
        await context.read<DocumentEditCubit>().updateDocument(mergedDocument);
        showSnackBar(context, S.of(context)!.documentSuccessfullyUpdated);
      } on PaperlessApiException catch (error, stackTrace) {
        showErrorMessage(context, error, stackTrace);
      } finally {
        // context.pop();
      }
    }
  }

  Widget _buildTitleFormField(String? initialTitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.title,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ),
        const Gap(4),
        FormBuilderTextField(
          name: fkTitle,
          decoration: InputDecoration(
            fillColor: AppColor.white,
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide.none,
            ),
            hintText: S.of(context)!.title,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 0, horizontal: 14),
            suffixIcon: IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () {
                _formKey.currentState?.fields[fkTitle]?.didChange(null);
              },
            ),
          ),
          initialValue: initialTitle,
        ),
      ],
    );
  }

  Widget _buildCreatedAtFormField(
      DateTime? initialCreatedAtDate, FieldSuggestions? filteredSuggestions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.createdAt,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ),
        const Gap(4),
        FormBuilderLocalizedDatePicker(
          name: fkCreatedDate,
          initialValue: initialCreatedAtDate,
          labelText: S.of(context)!.createdAt,
          firstDate: DateTime(1970, 1, 1),
          lastDate: DateTime(2100, 1, 1),
          locale: Localizations.localeOf(context),
          prefixIcon: const Icon(Icons.calendar_today),
        ),
        if (filteredSuggestions?.hasSuggestedDates ?? false)
          _buildSuggestionsSkeleton<DateTime>(
            suggestions: filteredSuggestions!.dates,
            itemBuilder: (context, itemData) => ActionChip(
              label: Text(
                  DateFormat.yMMMMd(Localizations.localeOf(context).toString())
                      .format(itemData)),
              onPressed: () => _formKey.currentState?.fields[fkCreatedDate]
                  ?.didChange(FormDateTime.fromDateTime(itemData)),
            ),
          ),
      ],
    );
  }

  ///
  /// Item builder is typically some sort of [Chip].
  ///
  Widget _buildSuggestionsSkeleton<T>({
    required Iterable<T> suggestions,
    required ItemBuilder<T> itemBuilder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.suggestions,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        SizedBox(
          height: 48,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: suggestions.length,
            itemBuilder: (context, index) => ColoredChipWrapper(
              child: itemBuilder(context, suggestions.elementAt(index)),
            ),
            separatorBuilder: (BuildContext context, int index) =>
                const SizedBox(width: 4.0),
          ),
        ),
      ],
    ).padded();
  }
}
