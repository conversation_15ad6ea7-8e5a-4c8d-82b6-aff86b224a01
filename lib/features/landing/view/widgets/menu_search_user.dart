import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/features/document_search/document_search_provider/document_search_provider.dart';
import 'package:paperless_mobile/features/settings/view/manage_accounts_page.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class MenuSearchUser extends StatelessWidget {
  const MenuSearchUser({super.key});

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
            onTap: () {
              Scaffold.of(context).openDrawer();
            },
            child: SvgPicture.asset('assets/svgs/menu.svg')),
        const Gap(8),
        GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DocumentSearchProvider(),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.only(left: 12),
            alignment: Alignment.centerLeft,
            height: 40,
            width: size.width - 112,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppColor.white,
            ),
            child: Row(
              children: [
                SvgPicture.asset('assets/svgs/Search.svg'),
                const Gap(8),
                Text(
                  S.of(context)!.searchDocuments,
                  style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColor.grey_9E9E9E),
                ),
              ],
            ),
          ),
        ),
        const Gap(8),
        GestureDetector(
            onTap: () {
              showDialog(
                context: context,
                builder: (_) => Provider.value(
                  value: context.read<LocalUserAccount>(),
                  child: const ManageAccountsPage(),
                ),
              );
            },
            child: SvgPicture.asset('assets/svgs/User.svg')),
      ],
    );
  }
}
