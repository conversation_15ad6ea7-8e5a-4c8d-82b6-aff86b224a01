import 'dart:developer' as dev;
import 'dart:io';
import 'package:cunning_document_scanner/cunning_document_scanner.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/constants.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/global/constants.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/app_drawer/view/app_drawer.dart';
import 'package:paperless_mobile/features/document_scan/cubit/document_scanner_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/document_upload_preparation_page.dart';
import 'package:paperless_mobile/features/home/<USER>/bottom_nav_bar_cubit.dart';
import 'package:paperless_mobile/features/landing/view/widgets/expansion_card.dart';
import 'package:paperless_mobile/features/landing/view/widgets/menu_search_user.dart';
import 'package:paperless_mobile/features/landing/view/widgets/mime_types_pie_chart.dart';
import 'package:paperless_mobile/features/login/cubit/authentication_cubit.dart';
import 'package:paperless_mobile/features/saved_view/cubit/saved_view_cubit.dart';
import 'package:paperless_mobile/features/saved_view_details/view/saved_view_preview.dart';
import 'package:paperless_mobile/features/settings/view/manage_accounts_page.dart';
import 'package:paperless_mobile/features/users/cubit/user_cubit.dart';
import 'package:paperless_mobile/features/users/cubit/user_state.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/saved_views_route.dart';
import 'package:paperless_mobile/routing/routes/scanner_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as p;

enum AddFileAction {
  scan,
  import,
}

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  final _searchBarHandle = SliverOverlapAbsorberHandle();
  late UserCubit userCubit;

  Future<bool> get _shouldShowChangelog async {
    try {
      final sp = await SharedPreferences.getInstance();
      final currentBuild = packageInfo.buildNumber;
      final existingVersions = sp.getStringList('changelogSeenForBuilds') ?? [];
      if (existingVersions.contains(currentBuild)) {
        return false;
      } else {
        existingVersions.add(currentBuild);
        await sp.setStringList('changelogSeenForBuilds', existingVersions);
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    userCubit = context.read();
    userCubit.getStorage();
  }

  Future<void> _openDocumentScanner(BuildContext context) async {
    final status = await Permission.camera.request();

    if (status == PermissionStatus.granted) {
      try {
        final imagesPath = await CunningDocumentScanner.getPictures(
          noOfPages: 1,
        );
        print(imagesPath?.first);

        if (imagesPath != null && imagesPath.isNotEmpty) {
          for (var element in imagesPath) {
            context.read<DocumentScannerCubit>().addScan(File(element));
          }
        }
      } on PlatformException {
        // 'Failed to get document path or operation cancelled!';
      }
    } else if (status == PermissionStatus.permanentlyDenied) {
      // Gợi ý mở settings nếu bị từ chối vĩnh viễn
      await openAppSettings();
    }
  }

  void _onUploadFromFilesystem() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions:
          supportedFileExtensions.map((e) => e.replaceAll(".", "")).toList(),
      withData: true,
      allowMultiple: false,
    );
    if (result?.files.single.path != null) {
      final path = result!.files.single.path!;
      final extension = p.extension(path);
      final filename = p.basenameWithoutExtension(path);
      File file = File(path);
      if (!supportedFileExtensions.contains(extension.toLowerCase())) {
        // ignore: use_build_context_synchronously
        showErrorMessage(
          context,
          const PaperlessApiException(ErrorCode.unsupportedFileFormat),
        );
        return;
      }
      // ignore: use_build_context_synchronously
      DocumentUploadRoute(
        $extra: file.readAsBytesSync(),
        filename: filename,
        title: filename,
        fileExtension: extension,
      ).push<DocumentUploadResult>(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = context.watch<LocalUserAccount>().paperlessUser;
    final Size size = MediaQuery.of(context).size;
    var children2 = [
      const Gap(10),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const MenuSearchUser(),
            const Gap(24),
            Text(
              S.of(context)!.welcomeUser(
                    currentUser.fullName ?? currentUser.username,
                  ),
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
      const Gap(24),
      _buildStorage(size),
      _buildStatisticsCard(context),
      if (currentUser.canViewSavedViews) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              SvgPicture.asset('assets/svgs/star.svg'),
              const Gap(10),
              const Text(
                'View',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
        BlocBuilder<SavedViewCubit, SavedViewState>(
          builder: (context, state) {
            return state.maybeWhen(
              loaded: (savedViews, storage) {
                final dashboardViews = savedViews.values
                    .where((element) => element.showOnDashboard)
                    .toList();
                if (dashboardViews.isEmpty) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context)!.youDidNotSaveAnyViewsYet,
                        style: Theme.of(context).textTheme.bodySmall,
                      ).padded(),
                      TextButton.icon(
                        onPressed: () {
                          const CreateSavedViewRoute(
                            showOnDashboard: true,
                          ).push(context);
                        },
                        icon: const Icon(
                          Icons.add,
                          color: AppColor.primary,
                        ),
                        label: Text(
                          S.of(context)!.newView,
                          style: const TextStyle(
                              color: AppColor.primary,
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ).paddedOnly(left: 16);
                }
                return ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    if (dashboardViews.isNotEmpty) {}
                    return SavedViewPreview(
                      savedView: dashboardViews.elementAt(index),
                      expanded: index == 0,
                    );
                  },
                  itemCount: dashboardViews.length,
                );
              },
              orElse: () => const SizedBox(),
            );
          },
        ),
      ],
      const SizedBox(
        height: 80,
      )
    ];
    return Scaffold(
      drawer: const AppDrawer(),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children2,
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await _showDialogAddFile(context);
          if (result == AddFileAction.scan) {
            // ignore: use_build_context_synchronously
            final BottomNavBarCubit cubit = context.read();
            cubit.changedIndexNavBar(2);
          }
        },
        shape: const CircleBorder(),
        backgroundColor: AppColor.primary,
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final currentUser = context.read<LocalUserAccount>().paperlessUser;
    return ExpansionCard(
      initiallyExpanded: true,
      title: 'Document Statistics',
      content: FutureBuilder<PaperlessServerStatisticsModel>(
        future: context.read<PaperlessServerStatsApi>().getServerStatistics(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(
              child: CircularProgressIndicator(),
            ).paddedOnly(top: 8, bottom: 24);
          }
          final stats = snapshot.data!;
          return Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 14),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 0.5,
                        color: AppColor.black_3C3C43.withOpacity(0.36))),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      height: 56,
                      child: Row(
                        children: [
                          const Text(
                            'Documents in inbox:',
                            style: AppTextStyles.textStyleBold14,
                          ),
                          const Spacer(),
                          Container(
                            height: 32,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 14,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.primary),
                            child: Text(
                              stats.documentsInInbox.toString(),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: 0.5,
                      width: size.width,
                      color: AppColor.black_3C3C43.withOpacity(0.36),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      height: 56,
                      width: size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context)!.totalDocuments),
                          Container(
                            height: 32,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 14,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.primary),
                            child: Text(
                              stats.documentsTotal.toString(),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: 0.5,
                      width: size.width,
                      color: AppColor.black_3C3C43.withOpacity(0.36),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      height: 56,
                      width: size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context)!.totalCharacters),
                          Container(
                            height: 32,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 14,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(
                              (stats.totalChars ?? 0).toString(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: 0.5,
                      width: size.width,
                      color: AppColor.black_3C3C43.withOpacity(0.36),
                    ),
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 0.5,
                                  color: AppColor.black_3C3C43
                                      .withOpacity(0.36)))),
                      child: Row(
                        children: [
                          const Text('Current ASN'),
                          const Spacer(),
                          Container(
                            alignment: Alignment.center,
                            height: 32,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(stats.currentAsn.toString()),
                          )
                        ],
                      ),
                    ),
                    if (stats.fileTypeCounts.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: MimeTypesPieChart(statistics: stats),
                      ),
                  ],
                ),
              ),
              const Gap(20),
              Container(
                width: size.width,
                margin: const EdgeInsets.symmetric(horizontal: 14),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 0.5,
                        color: AppColor.black_3C3C43.withOpacity(0.36))),
                child: Column(
                  children: [
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 0.5,
                                  color: AppColor.black_3C3C43
                                      .withOpacity(0.36)))),
                      child: Row(
                        children: [
                          const Text('Tags'),
                          const Spacer(),
                          Container(
                            alignment: Alignment.center,
                            height: 32,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(stats.tagCount.toString()),
                          )
                        ],
                      ),
                    ),
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 0.5,
                                  color: AppColor.black_3C3C43
                                      .withOpacity(0.36)))),
                      child: Row(
                        children: [
                          const Text('Correspondents'),
                          const Spacer(),
                          Container(
                            alignment: Alignment.center,
                            height: 32,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(stats.correspondentCount.toString()),
                          )
                        ],
                      ),
                    ),
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 0.5,
                                  color: AppColor.black_3C3C43
                                      .withOpacity(0.36)))),
                      child: Row(
                        children: [
                          const Text('Document types'),
                          const Spacer(),
                          Container(
                            alignment: Alignment.center,
                            height: 32,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(stats.documentTypeCount.toString()),
                          )
                        ],
                      ),
                    ),
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          const Text('Storage paths'),
                          const Spacer(),
                          Container(
                            alignment: Alignment.center,
                            height: 32,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: AppColor.grey_EBEDF4),
                            child: Text(stats.storagePathCount.toString()),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(20),
            ],
          );
        },
      ),
    );
  }

  Future<AddFileAction?> _showDialogAddFile(BuildContext context) {
    return showDialog<AddFileAction>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Stack(
          children: [
            Positioned(
              right: 16,
              bottom: 150, // khoảng cách so với FAB (điều chỉnh nếu cần)
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                elevation: 8,
                child: ConstrainedBox(
                  constraints: const BoxConstraints(minWidth: 240),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Center(
                          child: Text(
                            S.of(context)!.uploadDocument,
                            style: const TextStyle(
                                fontSize: 18, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      Container(color: AppColor.grey_909090, height: 1),
                      GestureDetector(
                        onTap: () async {
                          await _openDocumentScanner(context);
                          if (context.mounted) {
                            Navigator.pop(context, AddFileAction.scan);
                          }
                        },
                        child: Container(
                          height: 32,
                          width: 230,
                          color: Colors.transparent,
                          padding: const EdgeInsets.symmetric(horizontal: 14),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.of(context)!.scanDocument,
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              ),
                              SvgPicture.asset('assets/svgs/scan.svg')
                            ],
                          ),
                        ),
                      ),
                      const Divider(color: AppColor.grey_909090, height: 1),
                      GestureDetector(
                        onTap: () {
                          _onUploadFromFilesystem();
                          Navigator.pop(context, AddFileAction.import);
                        },
                        child: Container(
                          height: 32,
                          width: 230,
                          color: Colors.transparent,
                          padding: const EdgeInsets.symmetric(horizontal: 14),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.of(context)!.importDocument,
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              ),
                              SvgPicture.asset(
                                'assets/svgs/upload.svg',
                                colorFilter: const ColorFilter.mode(
                                    AppColor.primary, BlendMode.srcIn),
                              )
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String convertByte(int storageInKB) {
    if (storageInKB >= 1024 * 1024) {
      // >= 1 GB
      double gb = storageInKB / (1024 * 1024);
      return "${gb.toStringAsFixed(2)} GB";
    } else if (storageInKB >= 1024) {
      // >= 1 MB
      double mb = storageInKB / 1024;
      return "${mb.toStringAsFixed(2)} MB";
    } else {
      // < 1 MB, hiển thị KB
      return "$storageInKB KB";
    }
  }

  Widget _buildStorage(Size size) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        // Giả sử state.storage là đơn vị KB
        const double maxStorageKB = 100 * 1024 * 1024; // 100 GB in KB
        final double ratio =
            (state.storage / maxStorageKB).clamp(0.0, 1.0); // Clamp to [0,1]
        final double progressWidth = (size.width - 64) * ratio;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
              color: AppColor.white, borderRadius: BorderRadius.circular(10)),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.of(context)!.storageUsage,
                    style: AppTextStyles.textStyle14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${convertByte(state.storage)} of 100 GB used',
                    style: AppTextStyles.textStyle10
                        .copyWith(color: AppColor.grey_A9ACB4),
                  ),
                ],
              ),
              const Gap(10),
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Stack(
                  children: [
                    Container(
                      height: 10,
                      width: size.width - 64,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: AppColor.primary.withOpacity(0.1),
                      ),
                    ),
                    Container(
                      height: 10,
                      width: progressWidth,
                      decoration: const BoxDecoration(
                        color: AppColor.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
