import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_api/src/constants.dart';
import 'package:paperless_api/src/extensions/dio_exception_extension.dart';

class PaperlessDocumentsApiImpl implements PaperlessDocumentsApi {
  final Dio client;

  PaperlessDocumentsApiImpl(this.client);

  @override
  Future<String?> create(
    Uint8List documentBytes, {
    required String filename,
    required String title,
    String contentType = 'application/octet-stream',
    DateTime? createdAt,
    int? documentType,
    int? correspondent,
    Iterable<int> tags = const [],
    int? asn,
    void Function(double progress)? onProgressChanged,
    List<int>? departments,
  }) async {
    final formData = FormData();
    List<int> projects = [1, 2, 3];

    formData.files.add(
      MapEntry(
        'document',
        MultipartFile.fromBytes(documentBytes, filename: filename),
      ),
    );
    formData.fields.add(MapEntry('title', title));

    if (createdAt != null) {
      formData.fields.add(
        MapEntry('created', apiDateFormat.format(createdAt)),
      );
    }
    if (correspondent != null) {
      formData.fields.add(MapEntry('correspondent', jsonEncode(correspondent)));
    }
    if (documentType != null) {
      formData.fields.add(MapEntry('document_type', jsonEncode(documentType)));
    }
    if (asn != null) {
      formData.fields.add(MapEntry('archive_serial_number', jsonEncode(asn)));
    }
    if (departments != null && departments.isNotEmpty) {
      for (final dept in departments) {
        formData.fields.add(MapEntry('departments', dept.toString()));
      }
    }
    for (final tag in tags) {
      formData.fields.add(MapEntry('tags', tag.toString()));
    }
    for (final project in projects) {
      formData.fields.add(MapEntry('projects', project.toString()));
    }
    try {
      final response = await client.post(
        '/api/documents/post_document/',
        data: formData,
        onSendProgress: (count, total) {
          onProgressChanged?.call(count.toDouble() / total.toDouble());
        },
        options: Options(validateStatus: (status) => status == 200),
      );
      if (response.data != "OK") {
        return response.data as String;
      } else {
        return null;
      }
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.documentUploadFailed),
      );
    }
  }

  @override
  Future<DocumentModel> update(DocumentModel doc) async {
    try {
      final response = await client.put(
        "/api/documents/${doc.id}/",
        data: doc.toJson(),
        options: Options(validateStatus: (status) => status == 200),
      );
      return DocumentModel.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.documentUpdateFailed),
      );
    }
  }

  @override
  Future<PagedSearchResult<DocumentModel>> findAll(
    DocumentFilter filter,
  ) async {
    final filterParams = filter.toQueryParameters()
      ..addAll({'truncate_content': "true", "full_perms": true});
    try {
      final response = await client.get(
        "/api/documents/",
        queryParameters: filterParams,
        options: Options(validateStatus: (status) => status == 200),
      );
      return compute(
        PagedSearchResult.fromJsonSingleParam,
        PagedSearchResultJsonSerializer<DocumentModel>(
          response.data,
          DocumentModelJsonConverter(),
        ),
      );
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: PaperlessApiException(
          ErrorCode.documentLoadFailed,
          details: exception.message,
        ),
      );
    }
  }

  @override
  Future<int> delete(DocumentModel doc) async {
    try {
      await client.delete(
        "/api/documents/${doc.id}/",
        options: Options(validateStatus: (status) => status == 204),
      );

      return Future.value(doc.id);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.documentDeleteFailed),
      );
    }
  }

  @override
  String getThumbnailUrl(int documentId) {
    return "/api/documents/$documentId/thumb/";
  }

  String getPreviewUrl(int documentId) {
    return "/api/documents/$documentId/preview/";
  }

  @override
  Future<Uint8List> getPreview(int documentId) async {
    try {
      final response = await client.get(
        getPreviewUrl(documentId),
        options: Options(
          responseType: ResponseType.bytes,
          validateStatus: (status) => status == 200,
        ), //TODO: Check if bytes or stream is required
      );
      return response.data;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.documentPreviewFailed),
      );
    }
  }

  @override
  Future<int> findNextAsn() async {
    const DocumentFilter asnQueryFilter = DocumentFilter(
      sortField: SortField.archiveSerialNumber,
      sortOrder: SortOrder.descending,
      asnQuery: AnyAssignedIdQueryParameter(),
      page: 1,
      pageSize: 1,
    );
    try {
      final result = await findAll(asnQueryFilter);
      return result.results
              .map((e) => e.archiveSerialNumber)
              .firstWhere((asn) => asn != null, orElse: () => 0)! +
          1;
    } on PaperlessApiException {
      throw const PaperlessApiException(ErrorCode.documentAsnQueryFailed);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.documentAsnQueryFailed),
      );
    }
  }

  @override
  Future<Iterable<int>> bulkAction(BulkAction action) async {
    try {
      await client.post(
        "/api/documents/bulk_edit/",
        data: action.toJson(),
        options: Options(validateStatus: (status) => status == 200),
      );
      return action.documentIds;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(
          ErrorCode.documentBulkActionFailed,
        ),
      );
    }
  }

  @override
  Future<Uint8List> downloadDocument(
    int id, {
    bool original = false,
  }) async {
    try {
      final response = await client.get(
        "/api/documents/$id/download/",
        queryParameters: {'original': original},
        options: Options(responseType: ResponseType.bytes),
      );
      return response.data;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> downloadToFile(
    int id,
    String localFilePath, {
    bool original = false,
    void Function(double)? onProgressChanged,
  }) async {
    try {
      final response = await client.download(
        "/api/documents/$id/download/",
        localFilePath,
        onReceiveProgress: (count, total) =>
            onProgressChanged?.call(count / total),
        queryParameters: {'original': original},
      );
      return response.data;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<DocumentMetaData> getMetaData(int id) async {
    debugPrint("Fetching data for /api/documents/$id/metadata/...");

    try {
      final response = await client.get(
        "/api/documents/$id/metadata/",
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      debugPrint("Fetched data for /api/documents/$id/metadata/.");

      return DocumentMetaData.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<List<String>> autocomplete(String query, [int limit = 10]) async {
    try {
      final response = await client.get(
        '/api/search/autocomplete/',
        queryParameters: {
          'term': query,
          'limit': limit,
        },
        options: Options(validateStatus: (status) => status == 200),
      );
      return (response.data as List).cast<String>();
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(
          ErrorCode.autocompleteQueryError,
        ),
      );
    }
  }

  @override
  Future<FieldSuggestions> findSuggestions(DocumentModel document) async {
    try {
      final response = await client.get(
        "/api/documents/${document.id}/suggestions/",
        options: Options(validateStatus: (status) => status == 200),
      );
      return FieldSuggestions.fromJson(response.data)
          .forDocumentId(document.id);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.suggestionsQueryError),
      );
    }
  }

  @override
  Future<DocumentModel> find(int id) async {
    debugPrint("Fetching data from /api/documents/$id/...");
    try {
      final response = await client.get(
        "/api/documents/$id/",
        queryParameters: {'full_perms': true},
        options: Options(
          validateStatus: (status) => status == 200,
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      debugPrint("Fetched data for /api/documents/$id/.");
      return DocumentModel.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<DocumentModel> deleteNote(DocumentModel document, int noteId) async {
    try {
      final response = await client.delete(
        "/api/documents/${document.id}/notes/?id=$noteId",
        options: Options(validateStatus: (status) => status == 200),
      );
      final notes =
          (response.data as List).map((e) => NoteModel.fromJson(e)).toList();

      return document.copyWith(notes: notes);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.deleteNoteFailed),
      );
    }
  }

  @override
  Future<DocumentModel> addNote({
    required DocumentModel document,
    required String text,
  }) async {
    try {
      final response = await client.post(
        "/api/documents/${document.id}/notes/",
        options: Options(validateStatus: (status) => status == 200),
        data: {'note': text},
      );

      final notes =
          (response.data as List).map((e) => NoteModel.fromJson(e)).toList();

      return document.copyWith(notes: notes);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException(ErrorCode.addNoteFailed),
      );
    }
  }

  @override
  Future<List<UserPermission>> getUsers() async {
    try {
      final response = await client.get("/api/users/",
          options: Options(
            validateStatus: (status) => status == 200,
            sendTimeout: const Duration(seconds: 10),
            receiveTimeout: const Duration(seconds: 10),
          ),
          queryParameters: {'page': 1, 'page_size': 100000});
      final List<UserPermission> users = [];
      for (var i = 0; i < response.data['results'].length; i++) {
        final UserPermission user =
            UserPermission.fromJson(response.data['results'][i]);
        users.add(user);
      }

      return users;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<List<Group>> getGroups() async {
    try {
      final response = await client.get("/api/groups/",
          options: Options(
            validateStatus: (status) => status == 200,
            sendTimeout: const Duration(seconds: 10),
            receiveTimeout: const Duration(seconds: 10),
          ),
          queryParameters: {'page': 1, 'page_size': 100000});
      final List<Group> departments = [];
      for (var i = 0; i < response.data['results'].length; i++) {
        final Group user = Group.fromJson(response.data['results'][i]);
        departments.add(user);
      }

      return departments;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<AllProject> getAllProject() async {
    try {
      final response = await client.get("/api/projects/",
          options: Options(
            validateStatus: (status) => status == 200,
            sendTimeout: const Duration(seconds: 10),
            receiveTimeout: const Duration(seconds: 10),
          ),
          queryParameters: {'page': 1});

      return AllProject.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<Departments> getAllDepartments() async {
    try {
      final response = await client.get(
        "/api/departments/",
        options: Options(
          validateStatus: (status) => status == 200,
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      return Departments.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> updateDepartment(String id, String name) {
    try {
      final response = client.patch(
        "/api/departments/$id/",
        data: {'name': name},
        options: Options(validateStatus: (status) => status == 200),
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> createDepartment(String name) {
    try {
      final response = client.post(
        "/api/departments/",
        data: {
          'name': name,
        },
        options: Options(validateStatus: (status) => status == 201),
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> deleteDepartment(String id) {
    try {
      final response = client.delete(
        "/api/departments/$id/",
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<Departments> searchDepartment(String query) async {
    try {
      final response = await client.get("/api/departments/", queryParameters: {
        'name__icontains': query,
      });
      return Departments.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> createProject(String name) {
    try {
      final response = client.post(
        "/api/projects/",
        data: {
          'name': name,
        },
        options: Options(validateStatus: (status) => status == 201),
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> deleteProject(String id) {
    try {
      final response = client.delete(
        "/api/projects/$id/",
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<void> updateProject(String id, String name) {
    try {
      final response = client.patch(
        "/api/projects/$id/",
        data: {'name': name},
        options: Options(validateStatus: (status) => status == 200),
      );
      return response;
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }

  @override
  Future<AllProject> searchProject(String query) async {
    try {
      final response = await client.get("/api/projects/", queryParameters: {
        'name__icontains': query,
      });
      return AllProject.fromJson(response.data);
    } on DioException catch (exception) {
      throw exception.unravel(
        orElse: const PaperlessApiException.unknown(),
      );
    }
  }
}
